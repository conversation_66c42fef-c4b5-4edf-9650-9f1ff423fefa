{"manifest_version": 3, "name": "Twitter GraphQL 拦截器", "version": "1.0.0", "description": "拦截并展示Twitter GraphQL请求的Chrome扩展。", "permissions": ["activeTab", "scripting", "tabs", "storage"], "host_permissions": ["https://x.com/*", "https://twitter.com/*"], "action": {"default_popup": "popup/popup.html", "default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content/content.js"], "css": ["content/content.css"], "run_at": "document_idle"}, {"matches": ["https://x.com/*", "https://twitter.com/*"], "js": ["content/twitter-interceptor.js"], "run_at": "document_start"}], "web_accessible_resources": [{"resources": ["new_tab_page.html"], "matches": ["<all_urls>"]}]}