import { h } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { twitterDB } from '../utils/database';

function App() {
  const [stats, setStats] = useState({ cache: 0, tweets: 0 });
  const [loading, setLoading] = useState(true);

  const loadStats = async () => {
    try {
      setLoading(true);
      const [cacheCount, tweetsCount] = await Promise.all([
        twitterDB.getCacheCount(),
        twitterDB.getTweetsCount()
      ]);
      setStats({ cache: cacheCount, tweets: tweetsCount });
    } catch (error) {
      console.error('加载统计信息失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const openNewTab = () => {
    chrome.runtime.sendMessage({ action: "openNewExtensionTab" });
    window.close();
  };

  const toggleSidebar = () => {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
      if (tabs[0]?.id) {
        chrome.tabs.sendMessage(tabs[0].id, { action: "toggleSidebar" });
      }
    });
    window.close();
  };

  useEffect(() => {
    loadStats();
  }, []);

  return (
    <div className="app">
      <div className="header">
        <h1>🐦 Twitter 拦截器</h1>
        <p>拦截并收藏您喜欢的推文</p>
      </div>

      <div className="stats-section">
        <h3>📊 统计信息</h3>
        {loading ? (
          <div className="loading">加载中...</div>
        ) : (
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-number">{stats.cache}</span>
              <span className="stat-label">缓存推文</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{stats.tweets}</span>
              <span className="stat-label">收藏推文</span>
            </div>
          </div>
        )}
      </div>

      <div className="actions-section">
        <h3>🚀 快捷操作</h3>
        <div className="buttons">
          <button onClick={openNewTab} className="btn btn-primary">
            📱 查看收藏
          </button>
          <button onClick={toggleSidebar} className="btn btn-secondary">
            🔧 切换侧边栏
          </button>
          <button onClick={loadStats} className="btn btn-refresh">
            🔄 刷新统计
          </button>
        </div>
      </div>

      <div className="info-section">
        <h3>💡 使用提示</h3>
        <ul>
          <li>在 Twitter/X 上点赞或收藏推文</li>
          <li>推文会自动保存到扩展中</li>
          <li>点击"查看收藏"查看所有推文</li>
        </ul>
      </div>
    </div>
  );
}

export default App;