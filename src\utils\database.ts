/**
 * Twitter数据库管理模块
 * 管理cache和tweets两个IndexedDB数据库
 */

export interface TweetData {
  tweet_id: string;
  user_results: {
    image_url: string;
    created_at: string;
    name: string;
    screen_name: string;
  };
  legacy: {
    bookmarked: boolean;
    media_url_https?: string[];
    favorited: boolean;
    full_text: string;
  };
  timestamp: number; // 添加时间戳
}

class TwitterDatabase {
  private dbName = 'TwitterExtension';
  private version = 1;
  private db: IDBDatabase | null = null;

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        reject(new Error('数据库打开失败'));
      };

      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        // 创建cache表
        if (!db.objectStoreNames.contains('cache')) {
          const cacheStore = db.createObjectStore('cache', { keyPath: 'tweet_id' });
          cacheStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // 创建tweets表
        if (!db.objectStoreNames.contains('tweets')) {
          const tweetsStore = db.createObjectStore('tweets', { keyPath: 'tweet_id' });
          tweetsStore.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  async addToCache(tweetData: TweetData): Promise<void> {
    if (!this.db) await this.init();
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      
      tweetData.timestamp = Date.now();
      const request = store.put(tweetData);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('添加到缓存失败'));
    });
  }

  async moveToTweets(tweetId: string): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache', 'tweets'], 'readwrite');
      const cacheStore = transaction.objectStore('cache');
      const tweetsStore = transaction.objectStore('tweets');

      // 从cache中获取数据
      const getRequest = cacheStore.get(tweetId);
      
      getRequest.onsuccess = () => {
        const tweetData = getRequest.result;
        if (tweetData) {
          // 添加到tweets
          const addRequest = tweetsStore.put(tweetData);
          addRequest.onsuccess = () => {
            // 从cache中删除
            const deleteRequest = cacheStore.delete(tweetId);
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(new Error('从缓存删除失败'));
          };
          addRequest.onerror = () => reject(new Error('添加到推文失败'));
        } else {
          reject(new Error('在缓存中未找到推文'));
        }
      };
      
      getRequest.onerror = () => reject(new Error('获取缓存数据失败'));
    });
  }

  async getAllTweets(): Promise<TweetData[]> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['tweets'], 'readonly');
      const store = transaction.objectStore('tweets');
      const index = store.index('timestamp');
      
      // 按时间戳降序排列
      const request = index.openCursor(null, 'prev');
      const tweets: TweetData[] = [];

      request.onsuccess = () => {
        const cursor = request.result;
        if (cursor) {
          tweets.push(cursor.value);
          cursor.continue();
        } else {
          resolve(tweets);
        }
      };

      request.onerror = () => reject(new Error('获取推文失败'));
    });
  }

  async getCacheCount(): Promise<number> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');
      const request = store.count();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error('获取缓存计数失败'));
    });
  }

  async getTweetsCount(): Promise<number> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['tweets'], 'readonly');
      const store = transaction.objectStore('tweets');
      const request = store.count();

      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(new Error('获取推文计数失败'));
    });
  }

  async clearCache(): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('清空缓存失败'));
    });
  }

  async clearTweets(): Promise<void> {
    if (!this.db) await this.init();

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['tweets'], 'readwrite');
      const store = transaction.objectStore('tweets');
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('清空推文失败'));
    });
  }
}

// 导出单例实例
export const twitterDB = new TwitterDatabase();
