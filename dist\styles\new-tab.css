/* 新标签页样式 */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
               Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.new-tab-app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.app-title {
  font-size: 28px;
  font-weight: 700;
  color: #1da1f2;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-icon {
  font-size: 32px;
}

.stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  font-size: 14px;
  color: #666;
}

.stat-item strong {
  color: #1da1f2;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 过滤器样式 */
.filter-group {
  display: flex;
  background: #f8f9fa;
  border-radius: 25px;
  padding: 4px;
  gap: 2px;
}

.filter-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.filter-btn:hover {
  background: rgba(29, 161, 242, 0.1);
}

.filter-btn.active {
  background: #1da1f2;
  color: white;
}

/* 操作按钮样式 */
.action-group {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 10px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.action-btn:hover {
  background: #f8f9fa;
  border-color: #1da1f2;
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 下拉菜单样式 */
.dropdown {
  position: relative;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 1000;
  display: none;
}

.dropdown:hover .dropdown-content {
  display: block;
}

.dropdown-content button {
  width: 100%;
  padding: 10px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.dropdown-content button:hover {
  background: #f8f9fa;
}

.dropdown-content button:first-child {
  border-radius: 8px 8px 0 0;
}

.dropdown-content button:last-child {
  border-radius: 0 0 8px 8px;
}

/* 主内容区样式 */
.app-main {
  flex: 1;
  padding: 40px 20px;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* 加载状态样式 */
.loading-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

.loading-spinner.large {
  width: 60px;
  height: 60px;
  border-width: 6px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: white;
}

/* 瀑布流样式 */
.masonry-container {
  width: 100%;
}

.masonry-grid {
  width: 100%;
  position: relative;
}

.masonry-item {
  margin-bottom: 16px;
  transition: transform 0.2s ease;
}

.masonry-item:hover {
  transform: translateY(-2px);
}

/* 推文卡片样式 */
.tweet-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.tweet-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.tweet-header {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 700;
  font-size: 16px;
  color: #0f1419;
  margin-bottom: 2px;
}

.user-handle {
  font-size: 14px;
  color: #536471;
}

.tweet-time {
  font-size: 14px;
  color: #536471;
  flex-shrink: 0;
}

.tweet-content {
  margin-bottom: 16px;
}

.tweet-text {
  font-size: 16px;
  line-height: 1.5;
  color: #0f1419;
  word-wrap: break-word;
}

.tweet-link {
  color: #1da1f2;
  text-decoration: none;
}

.tweet-link:hover {
  text-decoration: underline;
}

.tweet-mention {
  color: #1da1f2;
  font-weight: 500;
}

.tweet-hashtag {
  color: #1da1f2;
  font-weight: 500;
}

/* 媒体样式 */
.tweet-media {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.media-item {
  width: 100%;
}

.media-item img {
  width: 100%;
  height: auto;
  display: block;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.media-item img:hover {
  transform: scale(1.02);
}

/* 操作状态样式 */
.tweet-actions {
  border-top: 1px solid #eff3f4;
  padding-top: 12px;
}

.action-indicators {
  display: flex;
  gap: 16px;
}

.action-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  font-weight: 500;
}

.action-indicator.liked {
  color: #f91880;
}

.action-indicator.bookmarked {
  color: #1da1f2;
}

.heart-icon,
.bookmark-icon {
  width: 16px;
  height: 16px;
  fill: currentColor;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: white;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 24px;
  margin-bottom: 12px;
  font-weight: 600;
}

.empty-state p {
  font-size: 16px;
  opacity: 0.8;
  max-width: 400px;
  margin: 0 auto;
}

/* 使用说明样式 */
.instructions {
  padding: 40px 20px;
  display: flex;
  justify-content: center;
}

.instruction-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 30px;
  max-width: 500px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.instruction-card h3 {
  margin-bottom: 20px;
  color: #1da1f2;
  font-size: 20px;
}

.instruction-card ol {
  padding-left: 20px;
}

.instruction-card li {
  margin-bottom: 12px;
  font-size: 16px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
  }
  
  .header-right {
    justify-content: space-between;
  }
  
  .app-main {
    padding: 20px 10px;
  }
  
  .tweet-card {
    padding: 16px;
  }
  
  .instruction-card {
    margin: 0 10px;
    padding: 20px;
  }
}
