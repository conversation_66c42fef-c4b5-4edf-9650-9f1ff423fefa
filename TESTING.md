# 测试指南

## 🧪 功能测试

### 1. 扩展安装测试

1. **构建项目**
   ```bash
   npm run build
   ```

2. **加载扩展**
   - 打开 `chrome://extensions/`
   - 开启开发者模式
   - 点击"加载已解压的扩展程序"
   - 选择 `dist` 文件夹

3. **验证安装**
   - 扩展图标应出现在工具栏
   - 点击图标应显示popup界面

### 2. Twitter拦截测试

1. **访问Twitter**
   - 打开 [https://x.com](https://x.com) 或 [https://twitter.com](https://twitter.com)
   - 登录您的账户

2. **验证拦截功能**
   - 打开浏览器开发者工具（F12）
   - 切换到Console标签
   - 刷新Twitter页面
   - 应该看到类似 "Twitter GraphQL拦截器已启动" 的日志

3. **测试数据缓存**
   - 浏览Twitter首页
   - 点击扩展图标
   - 查看"缓存推文"数量是否增加

### 3. 用户操作测试

1. **测试点赞功能**
   - 在Twitter上点赞一条推文
   - 观察控制台是否有 "推文 XXX 已移动到tweets数据库" 的日志
   - 点击扩展图标，查看"收藏推文"数量是否增加

2. **测试收藏功能**
   - 在Twitter上收藏一条推文
   - 同样观察日志和统计数量变化

### 4. 新标签页测试

1. **打开收藏页面**
   - 点击扩展图标
   - 点击"📱 查看收藏"按钮
   - 应该打开新标签页显示收藏的推文

2. **测试瀑布流布局**
   - 验证推文以卡片形式展示
   - 调整浏览器窗口大小，验证响应式布局
   - 测试推文卡片的点击功能（应跳转到原推文）

3. **测试过滤功能**
   - 点击"已喜欢"、"已收藏"、"全部"按钮
   - 验证推文列表是否正确过滤

### 5. 数据管理测试

1. **测试统计刷新**
   - 在popup中点击"🔄 刷新统计"
   - 验证数据是否更新

2. **测试数据清理**
   - 点击"🗑️ 清理"下拉菜单
   - 测试"清空缓存"、"清空推文"、"清空全部"功能
   - 验证数据是否正确清除

## 🐛 常见问题排查

### 拦截器不工作

1. **检查权限**
   - 确认扩展有访问 `https://x.com/*` 和 `https://twitter.com/*` 的权限

2. **检查控制台**
   - 查看是否有JavaScript错误
   - 确认拦截器初始化日志

3. **重新加载扩展**
   - 在 `chrome://extensions/` 中点击扩展的"重新加载"按钮

### 数据不显示

1. **检查IndexedDB**
   - 打开开发者工具 → Application → Storage → IndexedDB
   - 查看是否有 `TwitterExtension` 数据库
   - 检查 `cache` 和 `tweets` 表中的数据

2. **检查网络请求**
   - 在Network标签中查看GraphQL请求
   - 确认请求URL包含目标端点

### 样式问题

1. **检查CSS文件**
   - 确认 `dist/styles/new-tab.css` 文件存在
   - 检查浏览器是否正确加载样式文件

2. **清除缓存**
   - 硬刷新页面（Ctrl+Shift+R）
   - 清除浏览器缓存

## 📊 性能测试

### 内存使用

1. **监控内存**
   - 打开 `chrome://extensions/`
   - 点击扩展的"检查视图"
   - 在Performance标签中监控内存使用

2. **长时间使用测试**
   - 在Twitter上浏览30分钟以上
   - 检查内存是否有明显泄漏

### 响应速度

1. **拦截延迟**
   - 使用Performance工具测量拦截器对页面加载的影响
   - 确保延迟在可接受范围内（< 100ms）

2. **UI响应**
   - 测试popup打开速度
   - 测试新标签页加载速度

## 🔍 调试技巧

### 启用详细日志

在 `src/content/twitter-interceptor.ts` 中添加更多日志：

```typescript
console.log('拦截到请求:', url);
console.log('响应数据:', data);
console.log('提取的推文:', tweets);
```

### 检查数据库状态

在浏览器控制台中运行：

```javascript
// 检查数据库
const request = indexedDB.open('TwitterExtension');
request.onsuccess = function(event) {
  const db = event.target.result;
  console.log('数据库版本:', db.version);
  console.log('对象存储:', Array.from(db.objectStoreNames));
};
```

### 手动测试拦截器

```javascript
// 模拟GraphQL请求
fetch('https://x.com/i/api/graphql/test/TweetDetail', {
  method: 'POST',
  body: JSON.stringify({ test: 'data' })
});
```

## ✅ 测试清单

- [ ] 扩展成功安装
- [ ] Twitter页面拦截器启动
- [ ] 推文数据成功缓存
- [ ] 点赞操作正确处理
- [ ] 收藏操作正确处理
- [ ] Popup统计信息正确
- [ ] 新标签页正常显示
- [ ] 瀑布流布局正常
- [ ] 推文卡片点击跳转
- [ ] 过滤功能正常
- [ ] 数据清理功能正常
- [ ] 响应式设计正常
- [ ] 无明显性能问题
- [ ] 无JavaScript错误
