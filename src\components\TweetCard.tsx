/**
 * 推文卡片组件
 * 美观的卡片式推文展示
 */

import { h } from 'preact';
import { TweetData } from '../utils/database';
import { formatTime, processTwitterText } from '../utils/helpers';

interface TweetCardProps {
  tweet: TweetData;
  onImageLoad?: () => void;
}

export function TweetCard({ tweet, onImageLoad }: TweetCardProps) {
  const handleImageLoad = () => {
    if (onImageLoad) {
      onImageLoad();
    }
  };

  const handleImageError = (e: Event) => {
    const target = e.target as HTMLImageElement;
    target.style.display = 'none';
  };

  const openTweet = () => {
    const url = `https://x.com/${tweet.user_results.screen_name}/status/${tweet.tweet_id}`;
    window.open(url, '_blank');
  };

  return (
    <div className="tweet-card" onClick={openTweet}>
      {/* 用户信息头部 */}
      <div className="tweet-header">
        <div className="user-avatar">
          <img 
            src={tweet.user_results.image_url} 
            alt={tweet.user_results.name}
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
        <div className="user-info">
          <div className="user-name">{tweet.user_results.name}</div>
          <div className="user-handle">@{tweet.user_results.screen_name}</div>
        </div>
        <div className="tweet-time">
          {formatTime(tweet.user_results.created_at)}
        </div>
      </div>

      {/* 推文内容 */}
      <div className="tweet-content">
        <div 
          className="tweet-text"
          dangerouslySetInnerHTML={{ 
            __html: processTwitterText(tweet.legacy.full_text) 
          }}
        />
      </div>

      {/* 媒体内容 */}
      {tweet.legacy.media_url_https && tweet.legacy.media_url_https.length > 0 && (
        <div className="tweet-media">
          {tweet.legacy.media_url_https.map((url, index) => (
            <div key={index} className="media-item">
              <img 
                src={url} 
                alt="推文图片"
                onLoad={handleImageLoad}
                onError={handleImageError}
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(url, '_blank');
                }}
              />
            </div>
          ))}
        </div>
      )}

      {/* 互动状态 */}
      <div className="tweet-actions">
        <div className="action-indicators">
          {tweet.legacy.favorited && (
            <span className="action-indicator liked">
              <svg viewBox="0 0 24 24" className="heart-icon">
                <path d="M12 21.638h-.014C9.403 21.59 1.95 14.856 1.95 8.478c0-3.064 2.525-5.754 5.403-5.754 2.29 0 3.83 1.58 4.646 2.73.814-1.148 2.354-2.73 4.645-2.73 2.88 0 5.404 2.69 5.404 5.755 0 6.376-7.454 13.11-10.037 13.157H12z"/>
              </svg>
              已喜欢
            </span>
          )}
          {tweet.legacy.bookmarked && (
            <span className="action-indicator bookmarked">
              <svg viewBox="0 0 24 24" className="bookmark-icon">
                <path d="M4 4.5C4 3.12 5.119 2 6.5 2h11C18.881 2 20 3.12 20 4.5v18.44l-8-5.71-8 5.71V4.5z"/>
              </svg>
              已收藏
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
