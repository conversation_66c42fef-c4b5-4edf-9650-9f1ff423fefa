/**
 * 新标签页主应用组件
 * 展示收藏的推文
 */

import { h } from 'preact';
import { useState, useEffect } from 'preact/hooks';
import { twitterDB, TweetData } from '../utils/database';
import { Masonry } from '../components/Masonry';

export function NewTabApp() {
  const [tweets, setTweets] = useState<TweetData[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({ cache: 0, tweets: 0 });
  const [filter, setFilter] = useState<'all' | 'favorited' | 'bookmarked'>('all');

  // 加载推文数据
  const loadTweets = async () => {
    try {
      setLoading(true);
      const allTweets = await twitterDB.getAllTweets();
      setTweets(allTweets);
      
      // 加载统计信息
      const [cacheCount, tweetsCount] = await Promise.all([
        twitterDB.getCacheCount(),
        twitterDB.getTweetsCount()
      ]);
      
      setStats({ cache: cacheCount, tweets: tweetsCount });
    } catch (error) {
      console.error('加载推文失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 过滤推文
  const filteredTweets = tweets.filter(tweet => {
    switch (filter) {
      case 'favorited':
        return tweet.legacy.favorited;
      case 'bookmarked':
        return tweet.legacy.bookmarked;
      default:
        return true;
    }
  });

  // 清空数据
  const clearData = async (type: 'cache' | 'tweets' | 'all') => {
    if (!confirm(`确定要清空${type === 'cache' ? '缓存' : type === 'tweets' ? '推文' : '所有'}数据吗？`)) {
      return;
    }

    try {
      if (type === 'cache' || type === 'all') {
        await twitterDB.clearCache();
      }
      if (type === 'tweets' || type === 'all') {
        await twitterDB.clearTweets();
      }
      
      await loadTweets();
    } catch (error) {
      console.error('清空数据失败:', error);
    }
  };

  // 监听来自content script的消息
  useEffect(() => {
    const messageListener = (message: any) => {
      if (message.action === 'tweetMoved') {
        loadTweets(); // 重新加载数据
      }
    };

    chrome.runtime.onMessage.addListener(messageListener);
    
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener);
    };
  }, []);

  useEffect(() => {
    loadTweets();
  }, []);

  return (
    <div className="new-tab-app">
      {/* 头部 */}
      <header className="app-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="app-title">
              <span className="title-icon">🐦</span>
              Twitter 推文收藏
            </h1>
            <div className="stats">
              <span className="stat-item">
                缓存: <strong>{stats.cache}</strong>
              </span>
              <span className="stat-item">
                收藏: <strong>{stats.tweets}</strong>
              </span>
            </div>
          </div>
          
          <div className="header-right">
            {/* 过滤器 */}
            <div className="filter-group">
              <button 
                className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
                onClick={() => setFilter('all')}
              >
                全部
              </button>
              <button 
                className={`filter-btn ${filter === 'favorited' ? 'active' : ''}`}
                onClick={() => setFilter('favorited')}
              >
                已喜欢
              </button>
              <button 
                className={`filter-btn ${filter === 'bookmarked' ? 'active' : ''}`}
                onClick={() => setFilter('bookmarked')}
              >
                已收藏
              </button>
            </div>
            
            {/* 操作按钮 */}
            <div className="action-group">
              <button 
                className="action-btn refresh-btn"
                onClick={loadTweets}
                disabled={loading}
              >
                🔄 刷新
              </button>
              
              <div className="dropdown">
                <button className="action-btn dropdown-btn">
                  🗑️ 清理
                </button>
                <div className="dropdown-content">
                  <button onClick={() => clearData('cache')}>清空缓存</button>
                  <button onClick={() => clearData('tweets')}>清空推文</button>
                  <button onClick={() => clearData('all')}>清空全部</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 主内容区 */}
      <main className="app-main">
        <div className="content-container">
          {loading && tweets.length === 0 ? (
            <div className="loading-screen">
              <div className="loading-spinner large"></div>
              <p>正在加载推文...</p>
            </div>
          ) : (
            <Masonry 
              tweets={filteredTweets}
              loading={loading}
            />
          )}
        </div>
      </main>

      {/* 使用说明 */}
      {tweets.length === 0 && !loading && (
        <div className="instructions">
          <div className="instruction-card">
            <h3>📖 使用说明</h3>
            <ol>
              <li>在 Twitter/X 上浏览推文</li>
              <li>点击喜欢 ❤️ 或收藏 🔖 按钮</li>
              <li>推文会自动出现在这里</li>
              <li>点击推文卡片可以跳转到原推文</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
}
