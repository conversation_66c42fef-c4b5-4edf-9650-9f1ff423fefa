"use strict";
(() => {
  // src/utils/database.ts
  var TwitterDatabase = class {
    dbName = "TwitterExtension";
    version = 1;
    db = null;
    async init() {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open(this.dbName, this.version);
        request.onerror = () => {
          reject(new Error("\u6570\u636E\u5E93\u6253\u5F00\u5931\u8D25"));
        };
        request.onsuccess = () => {
          this.db = request.result;
          resolve();
        };
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          if (!db.objectStoreNames.contains("cache")) {
            const cacheStore = db.createObjectStore("cache", { keyPath: "tweet_id" });
            cacheStore.createIndex("timestamp", "timestamp", { unique: false });
          }
          if (!db.objectStoreNames.contains("tweets")) {
            const tweetsStore = db.createObjectStore("tweets", { keyPath: "tweet_id" });
            tweetsStore.createIndex("timestamp", "timestamp", { unique: false });
          }
        };
      });
    }
    async addToCache(tweetData) {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["cache"], "readwrite");
        const store = transaction.objectStore("cache");
        tweetData.timestamp = Date.now();
        const request = store.put(tweetData);
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error("\u6DFB\u52A0\u5230\u7F13\u5B58\u5931\u8D25"));
      });
    }
    async moveToTweets(tweetId) {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["cache", "tweets"], "readwrite");
        const cacheStore = transaction.objectStore("cache");
        const tweetsStore = transaction.objectStore("tweets");
        const getRequest = cacheStore.get(tweetId);
        getRequest.onsuccess = () => {
          const tweetData = getRequest.result;
          if (tweetData) {
            const addRequest = tweetsStore.put(tweetData);
            addRequest.onsuccess = () => {
              const deleteRequest = cacheStore.delete(tweetId);
              deleteRequest.onsuccess = () => resolve();
              deleteRequest.onerror = () => reject(new Error("\u4ECE\u7F13\u5B58\u5220\u9664\u5931\u8D25"));
            };
            addRequest.onerror = () => reject(new Error("\u6DFB\u52A0\u5230\u63A8\u6587\u5931\u8D25"));
          } else {
            reject(new Error("\u5728\u7F13\u5B58\u4E2D\u672A\u627E\u5230\u63A8\u6587"));
          }
        };
        getRequest.onerror = () => reject(new Error("\u83B7\u53D6\u7F13\u5B58\u6570\u636E\u5931\u8D25"));
      });
    }
    async getAllTweets() {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["tweets"], "readonly");
        const store = transaction.objectStore("tweets");
        const index = store.index("timestamp");
        const request = index.openCursor(null, "prev");
        const tweets = [];
        request.onsuccess = () => {
          const cursor = request.result;
          if (cursor) {
            tweets.push(cursor.value);
            cursor.continue();
          } else {
            resolve(tweets);
          }
        };
        request.onerror = () => reject(new Error("\u83B7\u53D6\u63A8\u6587\u5931\u8D25"));
      });
    }
    async getCacheCount() {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["cache"], "readonly");
        const store = transaction.objectStore("cache");
        const request = store.count();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(new Error("\u83B7\u53D6\u7F13\u5B58\u8BA1\u6570\u5931\u8D25"));
      });
    }
    async getTweetsCount() {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["tweets"], "readonly");
        const store = transaction.objectStore("tweets");
        const request = store.count();
        request.onsuccess = () => resolve(request.result);
        request.onerror = () => reject(new Error("\u83B7\u53D6\u63A8\u6587\u8BA1\u6570\u5931\u8D25"));
      });
    }
    async clearCache() {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["cache"], "readwrite");
        const store = transaction.objectStore("cache");
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error("\u6E05\u7A7A\u7F13\u5B58\u5931\u8D25"));
      });
    }
    async clearTweets() {
      if (!this.db) await this.init();
      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction(["tweets"], "readwrite");
        const store = transaction.objectStore("tweets");
        const request = store.clear();
        request.onsuccess = () => resolve();
        request.onerror = () => reject(new Error("\u6E05\u7A7A\u63A8\u6587\u5931\u8D25"));
      });
    }
  };
  var twitterDB = new TwitterDatabase();

  // src/content/twitter-interceptor.ts
  var GRAPHQL_ENDPOINTS = [
    "TweetDetail",
    "ModeratedTimeline",
    "UserTweets",
    "SearchTimeline",
    "HomeTimeline",
    "HomeLatestTimeline",
    "Bookmarks",
    "Likes",
    "ListLatestTweetsTimeline",
    "UserMedia"
  ];
  var USER_ACTION_ENDPOINTS = [
    "FavoriteTweet",
    "CreateBookmark"
  ];
  var TwitterInterceptor = class {
    originalFetch;
    originalXHROpen;
    originalXHRSend;
    constructor() {
      this.originalFetch = window.fetch;
      this.originalXHROpen = XMLHttpRequest.prototype.open;
      this.originalXHRSend = XMLHttpRequest.prototype.send;
      this.init();
    }
    init() {
      this.interceptFetch();
      this.interceptXHR();
      console.log("Twitter GraphQL\u62E6\u622A\u5668\u5DF2\u542F\u52A8");
    }
    interceptFetch() {
      window.fetch = async (input, init) => {
        const url = typeof input === "string" ? input : input.toString();
        try {
          const response = await this.originalFetch(input, init);
          if (this.isGraphQLRequest(url)) {
            this.handleGraphQLResponse(url, response.clone(), init);
          } else if (this.isUserActionRequest(url)) {
            this.handleUserAction(url, init);
          }
          return response;
        } catch (error) {
          console.error("Fetch\u62E6\u622A\u9519\u8BEF:", error);
          throw error;
        }
      };
    }
    interceptXHR() {
      const self = this;
      XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._url = url.toString();
        this._method = method;
        return self.originalXHROpen.call(this, method, url, ...args);
      };
      XMLHttpRequest.prototype.send = function(body) {
        const url = this._url;
        const method = this._method;
        if (self.isGraphQLRequest(url)) {
          this.addEventListener("load", function() {
            if (this.status === 200) {
              self.handleXHRResponse(url, this.responseText, body);
            }
          });
        } else if (self.isUserActionRequest(url)) {
          self.handleUserAction(url, { method, body });
        }
        return self.originalXHRSend.call(this, body);
      };
    }
    isGraphQLRequest(url) {
      return url.includes("/i/api/graphql/") && GRAPHQL_ENDPOINTS.some((endpoint) => url.includes(endpoint));
    }
    isUserActionRequest(url) {
      return url.includes("/i/api/graphql/") && USER_ACTION_ENDPOINTS.some((endpoint) => url.includes(endpoint));
    }
    async handleGraphQLResponse(url, response, init) {
      try {
        const data = await response.json();
        this.extractTweetData(data);
      } catch (error) {
        console.error("\u5904\u7406GraphQL\u54CD\u5E94\u9519\u8BEF:", error);
      }
    }
    handleXHRResponse(url, responseText, body) {
      try {
        const data = JSON.parse(responseText);
        this.extractTweetData(data);
      } catch (error) {
        console.error("\u5904\u7406XHR\u54CD\u5E94\u9519\u8BEF:", error);
      }
    }
    async handleUserAction(url, requestData) {
      try {
        let tweetId = null;
        if (requestData?.body) {
          const bodyStr = typeof requestData.body === "string" ? requestData.body : JSON.stringify(requestData.body);
          const bodyData = JSON.parse(bodyStr);
          tweetId = bodyData.variables?.tweet_id;
        }
        if (tweetId) {
          await twitterDB.moveToTweets(tweetId);
          console.log(`\u63A8\u6587 ${tweetId} \u5DF2\u79FB\u52A8\u5230tweets\u6570\u636E\u5E93`);
          chrome.runtime.sendMessage({
            action: "tweetMoved",
            tweetId
          });
        }
      } catch (error) {
        console.error("\u5904\u7406\u7528\u6237\u64CD\u4F5C\u9519\u8BEF:", error);
      }
    }
    async extractTweetData(data) {
      try {
        const tweets = this.findTweetsInData(data);
        for (const tweet of tweets) {
          const tweetData = this.parseTweetData(tweet);
          if (tweetData) {
            await twitterDB.addToCache(tweetData);
            console.log(`\u63A8\u6587 ${tweetData.tweet_id} \u5DF2\u6DFB\u52A0\u5230\u7F13\u5B58`);
          }
        }
      } catch (error) {
        console.error("\u63D0\u53D6\u63A8\u6587\u6570\u636E\u9519\u8BEF:", error);
      }
    }
    findTweetsInData(data) {
      const tweets = [];
      const traverse = (obj) => {
        if (obj && typeof obj === "object") {
          if (obj.tweet_id || obj.legacy && obj.legacy.id_str) {
            tweets.push(obj);
          }
          for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
              traverse(obj[key]);
            }
          }
        }
      };
      traverse(data);
      return tweets;
    }
    parseTweetData(tweet) {
      try {
        const tweetId = tweet.tweet_id || tweet.legacy?.id_str;
        if (!tweetId) return null;
        const userResult = tweet.core?.user_results?.result || tweet.user_results?.result;
        const legacy = tweet.legacy || {};
        const userLegacy = userResult?.legacy || {};
        return {
          tweet_id: tweetId,
          user_results: {
            image_url: userLegacy.profile_image_url_https || "",
            created_at: legacy.created_at || "",
            name: userLegacy.name || "",
            screen_name: userLegacy.screen_name || ""
          },
          legacy: {
            bookmarked: legacy.bookmarked || false,
            media_url_https: this.extractMediaUrls(legacy.entities?.media),
            favorited: legacy.favorited || false,
            full_text: legacy.full_text || legacy.text || ""
          },
          timestamp: Date.now()
        };
      } catch (error) {
        console.error("\u89E3\u6790\u63A8\u6587\u6570\u636E\u9519\u8BEF:", error);
        return null;
      }
    }
    extractMediaUrls(media) {
      if (!Array.isArray(media)) return [];
      return media.filter((item) => item.media_url_https).map((item) => item.media_url_https);
    }
  };
  new TwitterInterceptor();
})();
