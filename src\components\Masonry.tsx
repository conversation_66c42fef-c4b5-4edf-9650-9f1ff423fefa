/**
 * 瀑布流布局组件
 * 实现响应式的瀑布流展示
 */

import { h } from 'preact';
import { useState, useEffect, useRef } from 'preact/hooks';
import { TweetData } from '../utils/database';
import { TweetCard } from './TweetCard';
import { debounce } from '../utils/helpers';

interface MasonryProps {
  tweets: TweetData[];
  onLoadMore?: () => void;
  loading?: boolean;
}

export function Masonry({ tweets, onLoadMore, loading = false }: MasonryProps) {
  const [columns, setColumns] = useState(3);
  const [columnHeights, setColumnHeights] = useState<number[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const itemRefs = useRef<Map<string, HTMLDivElement>>(new Map());

  // 计算列数
  const calculateColumns = () => {
    if (!containerRef.current) return;
    
    const containerWidth = containerRef.current.offsetWidth;
    const minColumnWidth = 320; // 最小列宽
    const gap = 16; // 间距
    
    const newColumns = Math.max(1, Math.floor((containerWidth + gap) / (minColumnWidth + gap)));
    
    if (newColumns !== columns) {
      setColumns(newColumns);
      setColumnHeights(new Array(newColumns).fill(0));
    }
  };

  // 防抖的窗口大小调整处理
  const debouncedResize = debounce(calculateColumns, 250);

  useEffect(() => {
    calculateColumns();
    window.addEventListener('resize', debouncedResize);
    
    return () => {
      window.removeEventListener('resize', debouncedResize);
    };
  }, []);

  useEffect(() => {
    setColumnHeights(new Array(columns).fill(0));
  }, [columns]);

  // 获取最短列的索引
  const getShortestColumnIndex = () => {
    return columnHeights.indexOf(Math.min(...columnHeights));
  };

  // 处理图片加载完成，重新计算布局
  const handleImageLoad = (tweetId: string) => {
    const element = itemRefs.current.get(tweetId);
    if (element) {
      // 延迟一点时间确保图片完全加载
      setTimeout(() => {
        calculateLayout();
      }, 100);
    }
  };

  // 计算布局
  const calculateLayout = () => {
    const newHeights = new Array(columns).fill(0);
    
    tweets.forEach((tweet, index) => {
      const element = itemRefs.current.get(tweet.tweet_id);
      if (element) {
        const columnIndex = getShortestColumnIndex();
        const top = newHeights[columnIndex];
        const left = columnIndex * (100 / columns);
        
        element.style.position = 'absolute';
        element.style.top = `${top}px`;
        element.style.left = `${left}%`;
        element.style.width = `calc(${100 / columns}% - 12px)`;
        
        newHeights[columnIndex] += element.offsetHeight + 16; // 16px gap
      }
    });
    
    setColumnHeights(newHeights);
    
    // 设置容器高度
    if (containerRef.current) {
      const maxHeight = Math.max(...newHeights);
      containerRef.current.style.height = `${maxHeight}px`;
    }
  };

  // 当推文数据变化时重新计算布局
  useEffect(() => {
    if (tweets.length > 0) {
      // 延迟计算，确保DOM已更新
      setTimeout(calculateLayout, 100);
    }
  }, [tweets, columns]);

  // 无限滚动检测
  useEffect(() => {
    const handleScroll = debounce(() => {
      if (!onLoadMore || loading) return;
      
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;
      
      // 当滚动到距离底部200px时加载更多
      if (scrollTop + windowHeight >= documentHeight - 200) {
        onLoadMore();
      }
    }, 100);

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [onLoadMore, loading]);

  return (
    <div className="masonry-container">
      <div 
        ref={containerRef}
        className="masonry-grid"
        style={{ position: 'relative' }}
      >
        {tweets.map((tweet) => (
          <div
            key={tweet.tweet_id}
            ref={(el) => {
              if (el) {
                itemRefs.current.set(tweet.tweet_id, el);
              } else {
                itemRefs.current.delete(tweet.tweet_id);
              }
            }}
            className="masonry-item"
          >
            <TweetCard 
              tweet={tweet} 
              onImageLoad={() => handleImageLoad(tweet.tweet_id)}
            />
          </div>
        ))}
      </div>
      
      {loading && (
        <div className="loading-indicator">
          <div className="loading-spinner"></div>
          <span>加载中...</span>
        </div>
      )}
      
      {tweets.length === 0 && !loading && (
        <div className="empty-state">
          <div className="empty-icon">📱</div>
          <h3>暂无推文</h3>
          <p>当您在Twitter上点赞或收藏推文时，它们会出现在这里</p>
        </div>
      )}
    </div>
  );
}
