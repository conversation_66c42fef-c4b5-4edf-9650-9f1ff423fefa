{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "dist/background.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:background": "esbuild src/background.ts --bundle --outfile=dist/background.js --platform=browser", "build:content": "esbuild src/content/content.ts --bundle --outfile=dist/content/content.js --platform=browser", "build:twitter-interceptor": "esbuild src/content/twitter-interceptor.ts --bundle --outfile=dist/content/twitter-interceptor.js --platform=browser", "build:popup": "esbuild src/popup/popup.ts --bundle --outfile=dist/popup/popup.js --platform=browser --jsx-factory=h --jsx-fragment=Fragment", "build:new-tab": "esbuild src/new-tab/new-tab.ts --bundle --outfile=dist/new-tab/new-tab.js --platform=browser --jsx-factory=h --jsx-fragment=Fragment", "copy-static": "cpx \"{icons/**/*,popup/**/*.html,popup/**/*.css,content/**/*.css,styles/**/*.css,new_tab_page.html,manifest.json}\" dist", "prebuild": "npm run copy-static", "build": "npm run build:background && npm run build:content && npm run build:twitter-interceptor && npm run build:popup && npm run build:new-tab"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/chrome": "^0.0.269", "cpx": "^1.5.0", "esbuild": "^0.21.4", "preact": "^10.22.0", "typescript": "^5.4.5"}}