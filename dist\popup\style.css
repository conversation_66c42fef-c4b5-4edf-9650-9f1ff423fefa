/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
               Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  line-height: 1.6;
  width: 380px;
  height: 500px;
}

/* 应用容器 */
.app {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #1da1f2 0%, #0d8bd9 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

.header h1 {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.header p {
  font-size: 13px;
  opacity: 0.9;
}

/* 统计信息样式 */
.stats-section {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
}

.stats-section h3 {
  font-size: 14px;
  margin-bottom: 12px;
  color: #333;
}

.loading {
  text-align: center;
  color: #666;
  font-size: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.stat-number {
  display: block;
  font-size: 20px;
  font-weight: 700;
  color: #1da1f2;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 11px;
  color: #666;
  font-weight: 500;
}


/* 操作按钮样式 */
.actions-section {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
}

.actions-section h3 {
  font-size: 14px;
  margin-bottom: 12px;
  color: #333;
}

.buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  padding: 10px 14px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-primary {
  background: #1da1f2;
  color: white;
}

.btn-primary:hover {
  background: #0d8bd9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

.btn-secondary:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.btn-refresh {
  background: #28a745;
  color: white;
}

.btn-refresh:hover {
  background: #218838;
  transform: translateY(-1px);
}

/* 信息提示样式 */
.info-section {
  padding: 15px;
  flex: 1;
}

.info-section h3 {
  font-size: 14px;
  margin-bottom: 12px;
  color: #333;
}

.info-section ul {
  list-style: none;
  padding: 0;
}

.info-section li {
  font-size: 11px;
  color: #666;
  margin-bottom: 6px;
  padding-left: 16px;
  position: relative;
}

.info-section li::before {
  content: '•';
  color: #1da1f2;
  font-weight: bold;
  position: absolute;
  left: 0;
}