/**
 * 工具函数模块
 * 提供时间格式化、文本处理等通用功能
 */

/**
 * 格式化时间显示
 * @param dateString Twitter的时间字符串
 * @returns 格式化后的时间字符串
 */
export function formatTime(dateString: string): string {
  if (!dateString) return '';
  
  try {
    const date = new Date(dateString);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  } catch (error) {
    console.error('时间格式化错误:', error);
    return dateString;
  }
}

/**
 * 截断文本并添加省略号
 * @param text 原始文本
 * @param maxLength 最大长度
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number = 280): string {
  if (!text || text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
}

/**
 * 提取文本中的链接
 * @param text 原始文本
 * @returns 包含链接信息的对象
 */
export function extractLinks(text: string): { text: string; links: string[] } {
  const urlRegex = /(https?:\/\/[^\s]+)/g;
  const links: string[] = [];
  const matches = text.match(urlRegex);
  
  if (matches) {
    links.push(...matches);
  }
  
  return { text, links };
}

/**
 * 处理推文文本，转换链接和提及
 * @param text 原始推文文本
 * @returns 处理后的HTML字符串
 */
export function processTwitterText(text: string): string {
  if (!text) return '';
  
  // 转义HTML特殊字符
  let processedText = text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
  
  // 处理链接
  processedText = processedText.replace(
    /(https?:\/\/[^\s]+)/g,
    '<a href="$1" target="_blank" rel="noopener noreferrer" class="tweet-link">$1</a>'
  );
  
  // 处理@提及
  processedText = processedText.replace(
    /@(\w+)/g,
    '<span class="tweet-mention">@$1</span>'
  );
  
  // 处理#标签
  processedText = processedText.replace(
    /#(\w+)/g,
    '<span class="tweet-hashtag">#$1</span>'
  );
  
  return processedText;
}

/**
 * 生成随机ID
 * @returns 随机字符串ID
 */
export function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func.apply(null, args), wait);
  };
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 时间限制（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func.apply(null, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * 检查图片URL是否有效
 * @param url 图片URL
 * @returns Promise<boolean>
 */
export function isValidImageUrl(url: string): Promise<boolean> {
  return new Promise((resolve) => {
    if (!url) {
      resolve(false);
      return;
    }
    
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
    
    // 5秒超时
    setTimeout(() => resolve(false), 5000);
  });
}

/**
 * 格式化数字显示（如点赞数、转发数）
 * @param num 数字
 * @returns 格式化后的字符串
 */
export function formatNumber(num: number): string {
  if (num < 1000) return num.toString();
  if (num < 1000000) return (num / 1000).toFixed(1) + 'K';
  return (num / 1000000).toFixed(1) + 'M';
}

/**
 * 获取图片的主色调
 * @param imageUrl 图片URL
 * @returns Promise<string> 颜色值
 */
export function getDominantColor(imageUrl: string): Promise<string> {
  return new Promise((resolve) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          resolve('#1da1f2'); // Twitter蓝色作为默认值
          return;
        }
        
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const data = imageData.data;
        
        let r = 0, g = 0, b = 0;
        const pixelCount = data.length / 4;
        
        for (let i = 0; i < data.length; i += 4) {
          r += data[i];
          g += data[i + 1];
          b += data[i + 2];
        }
        
        r = Math.floor(r / pixelCount);
        g = Math.floor(g / pixelCount);
        b = Math.floor(b / pixelCount);
        
        resolve(`rgb(${r}, ${g}, ${b})`);
      } catch (error) {
        resolve('#1da1f2');
      }
    };
    
    img.onerror = () => resolve('#1da1f2');
    img.src = imageUrl;
  });
}
