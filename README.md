# Twitter GraphQL 拦截器

一个功能强大的Chrome扩展，用于拦截和收藏Twitter/X上的推文。

## 🌟 功能特性

### 🔍 智能拦截
- **双重拦截**：同时拦截 `fetch` 和 `XMLHttpRequest` 请求
- **精准识别**：自动识别以下GraphQL端点的推文数据：
  - TweetDetail（推文详情）
  - ModeratedTimeline（时间线）
  - UserTweets（用户推文）
  - SearchTimeline（搜索结果）
  - HomeTimeline（首页时间线）
  - HomeLatestTimeline（最新时间线）
  - Bookmarks（书签）
  - Likes（喜欢）
  - ListLatestTweetsTimeline（列表时间线）
  - UserMedia（用户媒体）

### 📊 数据管理
- **IndexedDB存储**：使用两个数据库进行数据管理
  - `cache` 数据库：存储所有拦截到的推文
  - `tweets` 数据库：存储用户主动收藏的推文
- **自动转移**：当用户点赞或收藏推文时，自动从cache转移到tweets数据库

### 🎨 美观展示
- **瀑布流布局**：响应式的瀑布流展示收藏的推文
- **卡片式设计**：美观的推文卡片，包含：
  - 用户头像和信息
  - 推文内容（支持链接、@提及、#标签）
  - 媒体图片
  - 互动状态（已喜欢/已收藏）

### 🚀 便捷操作
- **实时统计**：显示缓存和收藏推文数量
- **快捷访问**：通过扩展popup快速查看统计和操作
- **数据管理**：支持清空缓存、推文或全部数据

## 📦 安装方法

### 开发环境安装

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd augmentcodeX
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **构建项目**
   ```bash
   npm run build
   ```

4. **加载扩展**
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目中的 `dist` 文件夹

## 🎯 使用方法

### 基本使用

1. **安装扩展**后，访问 [Twitter/X](https://x.com)
2. **正常浏览**推文，扩展会自动拦截并缓存推文数据
3. **点赞或收藏**感兴趣的推文
4. **点击扩展图标**查看统计信息
5. **点击"查看收藏"**打开新标签页查看所有收藏的推文

### 高级功能

- **过滤查看**：在新标签页中可以按"全部"、"已喜欢"、"已收藏"进行过滤
- **数据管理**：通过popup中的清理功能管理存储的数据
- **实时更新**：推文数据会实时同步更新

## 🏗️ 项目结构

```
src/
├── background.ts              # 后台脚本
├── content/
│   ├── content.ts            # 通用内容脚本
│   └── twitter-interceptor.ts # Twitter拦截器
├── components/
│   ├── TweetCard.tsx         # 推文卡片组件
│   └── Masonry.tsx           # 瀑布流组件
├── new-tab/
│   ├── NewTabApp.tsx         # 新标签页应用
│   └── new-tab.ts            # 新标签页入口
├── popup/
│   └── App.tsx               # 弹窗应用
└── utils/
    ├── database.ts           # 数据库管理
    └── helpers.ts            # 工具函数
```

## 🔧 技术栈

- **前端框架**：Preact
- **构建工具**：esbuild
- **数据存储**：IndexedDB
- **样式**：原生CSS
- **类型检查**：TypeScript

## 📝 开发说明

### 构建命令

```bash
# 构建所有模块
npm run build

# 单独构建
npm run build:background      # 后台脚本
npm run build:content         # 内容脚本
npm run build:twitter-interceptor  # Twitter拦截器
npm run build:popup           # 弹窗
npm run build:new-tab         # 新标签页

# 复制静态文件
npm run copy-static
```

### 开发模式

1. 修改源代码
2. 运行 `npm run build`
3. 在Chrome扩展管理页面点击"重新加载"

## 🛡️ 隐私说明

- 本扩展仅在本地存储数据，不会上传到任何服务器
- 所有拦截的数据都存储在用户的浏览器本地IndexedDB中
- 扩展不会收集或传输任何个人信息

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 支持

如果您在使用过程中遇到问题，请：

1. 检查浏览器控制台是否有错误信息
2. 确认扩展权限已正确授予
3. 尝试重新加载扩展
4. 提交Issue描述具体问题
