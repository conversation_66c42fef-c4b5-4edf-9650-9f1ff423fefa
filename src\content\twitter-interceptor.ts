/**
 * Twitter GraphQL请求拦截器
 * 拦截fetch和XMLHttpRequest请求，识别并处理Twitter GraphQL响应
 */

import { twitterDB, TweetData } from '../utils/database';

// GraphQL端点标识符
const GRAPHQL_ENDPOINTS = [
  'TweetDetail',
  'ModeratedTimeline', 
  'UserTweets',
  'SearchTimeline',
  'HomeTimeline',
  'HomeLatestTimeline',
  'Bookmarks',
  'Likes',
  'ListLatestTweetsTimeline',
  'UserMedia'
];

// 用户操作端点
const USER_ACTION_ENDPOINTS = [
  'FavoriteTweet',
  'CreateBookmark'
];

class TwitterInterceptor {
  private originalFetch: typeof fetch;
  private originalXHROpen: typeof XMLHttpRequest.prototype.open;
  private originalXHRSend: typeof XMLHttpRequest.prototype.send;

  constructor() {
    this.originalFetch = window.fetch;
    this.originalXHROpen = XMLHttpRequest.prototype.open;
    this.originalXHRSend = XMLHttpRequest.prototype.send;
    
    this.init();
  }

  private init(): void {
    this.interceptFetch();
    this.interceptXHR();
    console.log('Twitter GraphQL拦截器已启动');
  }

  private interceptFetch(): void {
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
      const url = typeof input === 'string' ? input : input.toString();
      
      try {
        const response = await this.originalFetch(input, init);
        
        if (this.isGraphQLRequest(url)) {
          this.handleGraphQLResponse(url, response.clone(), init);
        } else if (this.isUserActionRequest(url)) {
          this.handleUserAction(url, init);
        }
        
        return response;
      } catch (error) {
        console.error('Fetch拦截错误:', error);
        throw error;
      }
    };
  }

  private interceptXHR(): void {
    const self = this;
    
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      (this as any)._url = url.toString();
      (this as any)._method = method;
      return self.originalXHROpen.call(this, method, url, ...args);
    };

    XMLHttpRequest.prototype.send = function(body?: Document | XMLHttpRequestBodyInit | null) {
      const url = (this as any)._url;
      const method = (this as any)._method;
      
      if (self.isGraphQLRequest(url)) {
        this.addEventListener('load', function() {
          if (this.status === 200) {
            self.handleXHRResponse(url, this.responseText, body);
          }
        });
      } else if (self.isUserActionRequest(url)) {
        self.handleUserAction(url, { method, body });
      }
      
      return self.originalXHRSend.call(this, body);
    };
  }

  private isGraphQLRequest(url: string): boolean {
    return url.includes('/i/api/graphql/') && 
           GRAPHQL_ENDPOINTS.some(endpoint => url.includes(endpoint));
  }

  private isUserActionRequest(url: string): boolean {
    return url.includes('/i/api/graphql/') && 
           USER_ACTION_ENDPOINTS.some(endpoint => url.includes(endpoint));
  }

  private async handleGraphQLResponse(url: string, response: Response, init?: RequestInit): Promise<void> {
    try {
      const data = await response.json();
      this.extractTweetData(data);
    } catch (error) {
      console.error('处理GraphQL响应错误:', error);
    }
  }

  private handleXHRResponse(url: string, responseText: string, body?: Document | XMLHttpRequestBodyInit | null): void {
    try {
      const data = JSON.parse(responseText);
      this.extractTweetData(data);
    } catch (error) {
      console.error('处理XHR响应错误:', error);
    }
  }

  private async handleUserAction(url: string, requestData?: any): Promise<void> {
    try {
      let tweetId: string | null = null;
      
      if (requestData?.body) {
        const bodyStr = typeof requestData.body === 'string' ? 
          requestData.body : JSON.stringify(requestData.body);
        
        const bodyData = JSON.parse(bodyStr);
        tweetId = bodyData.variables?.tweet_id;
      }
      
      if (tweetId) {
        await twitterDB.moveToTweets(tweetId);
        console.log(`推文 ${tweetId} 已移动到tweets数据库`);
        
        // 通知popup更新
        chrome.runtime.sendMessage({
          action: 'tweetMoved',
          tweetId: tweetId
        });
      }
    } catch (error) {
      console.error('处理用户操作错误:', error);
    }
  }

  private async extractTweetData(data: any): Promise<void> {
    try {
      const tweets = this.findTweetsInData(data);
      
      for (const tweet of tweets) {
        const tweetData = this.parseTweetData(tweet);
        if (tweetData) {
          await twitterDB.addToCache(tweetData);
          console.log(`推文 ${tweetData.tweet_id} 已添加到缓存`);
        }
      }
    } catch (error) {
      console.error('提取推文数据错误:', error);
    }
  }

  private findTweetsInData(data: any): any[] {
    const tweets: any[] = [];
    
    const traverse = (obj: any) => {
      if (obj && typeof obj === 'object') {
        if (obj.tweet_id || (obj.legacy && obj.legacy.id_str)) {
          tweets.push(obj);
        }
        
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            traverse(obj[key]);
          }
        }
      }
    };
    
    traverse(data);
    return tweets;
  }

  private parseTweetData(tweet: any): TweetData | null {
    try {
      const tweetId = tweet.tweet_id || tweet.legacy?.id_str;
      if (!tweetId) return null;

      const userResult = tweet.core?.user_results?.result || tweet.user_results?.result;
      const legacy = tweet.legacy || {};
      const userLegacy = userResult?.legacy || {};

      return {
        tweet_id: tweetId,
        user_results: {
          image_url: userLegacy.profile_image_url_https || '',
          created_at: legacy.created_at || '',
          name: userLegacy.name || '',
          screen_name: userLegacy.screen_name || ''
        },
        legacy: {
          bookmarked: legacy.bookmarked || false,
          media_url_https: this.extractMediaUrls(legacy.entities?.media),
          favorited: legacy.favorited || false,
          full_text: legacy.full_text || legacy.text || ''
        },
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('解析推文数据错误:', error);
      return null;
    }
  }

  private extractMediaUrls(media: any[]): string[] {
    if (!Array.isArray(media)) return [];
    
    return media
      .filter(item => item.media_url_https)
      .map(item => item.media_url_https);
  }
}

// 初始化拦截器
new TwitterInterceptor();
